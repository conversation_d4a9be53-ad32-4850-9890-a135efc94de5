# وثيقة تحليل المواصفات الفنية
## تطبيق "أذكار المسلم" 📿

---

## 📋 نظرة عامة على المشروع

### اسم التطبيق
**أذكار المسلم** - تطبيق إسلامي شامل للأذكار والقرآن الكريم

### الهدف من التطبيق
تطبيق إسلامي متكامل يوفر للمسلمين أدواتهم اليومية الأساسية من أذكار وقرآن ومواقيت صلاة، مع التركيز على البساطة والسرعة والعمل بدون اتصال إنترنت.

### المنصة المستهدفة
- **النظام**: Android (الحد الأدنى: Android 5.0 - API Level 21)
- **تقنية التطوير**: Flutter
- **اللغة**: العربية فقط
- **الجمهور المستهدف**: جميع الفئات العمرية

---

## 🎨 التصميم والواجهة

### نظام الألوان
- **اللون الأساسي**: الأخضر الإسلامي (#2E7D32)
- **اللون الثانوي**: الذهبي (#FFB300)
- **خلفية فاتحة**: أبيض مائل للأخضر (#F1F8E9)
- **خلفية داكنة**: أخضر داكن (#1B5E20)
- **نص أساسي**: أسود (#212121)
- **نص ثانوي**: رمادي داكن (#424242)

### الخطوط
- **الخط الأساسي**: Amiri أو Scheherazade New
- **القرآن الكريم**: خط عثماني (KFGQPC Uthmanic Script HAFS)
- **أحجام الخط**: قابلة للتخصيص (صغير، متوسط، كبير، كبير جداً)

### الأيقونات والرموز التعبيرية
- 🕌 للمساجد ومواقيت الصلاة
- 📿 للأذكار والتسبيح
- 📖 للقرآن الكريم
- 🧭 لاتجاه القبلة
- 🌙 للتقويم الهجري
- ✨ لأسماء الله الحسنى
- 📅 للتقويم والأعمال الصالحة
- 🔍 للبحث
- 📋 للنسخ
- 📤 للمشاركة

---

## 🔧 المتطلبات التقنية

### البيئة التقنية
```yaml
Framework: Flutter 3.16+
Language: Dart 3.0+
Target SDK: Android 34
Min SDK: Android 21 (5.0)
Architecture: ARM64, ARMv7
Size Target: < 50 MB
```

### المكتبات الأساسية
- **sqflite**: قاعدة البيانات المحلية
- **geolocator**: تحديد الموقع الجغرافي
- **flutter_compass**: البوصلة لاتجاه القبلة
- **flutter_local_notifications**: الإشعارات المحلية
- **shared_preferences**: حفظ الإعدادات
- **hijri**: التقويم الهجري
- **adhan**: حساب مواقيت الصلاة
- **vibration**: الاهتزاز
- **share_plus**: مشاركة النصوص
- **flutter_screenutil**: تجاوب التصميم

---

## 📱 الميزات الأساسية

### 1. أذكار المسلم 📿

#### المصدر
- **المرجع الأساسي**: حصن المسلم للشيخ سعيد بن وهف القحطاني
- **التصنيف**: حسب الأوقات والمناسبات

#### التصنيفات الرئيسية
1. **أذكار الصباح** (بعد صلاة الفجر)
2. **أذكار المساء** (بعد صلاة العصر)
3. **أذكار بعد الصلاة**
4. **أذكار النوم**
5. **أذكار الاستيقاظ**
6. **أذكار دخول المنزل والخروج منه**
7. **أذكار دخول المسجد والخروج منه**
8. **أذكار الوضوء**
9. **أذكار السفر**
10. **أذكار الطعام والشراب**
11. **أذكار المطر والرعد**
12. **أذكار الخوف والقلق**
13. **أذكار المرض**
14. **أذكار الزواج**
15. **أذكار متنوعة**

#### الميزات التفاعلية
- **عداد التكرار**: لكل ذكر مع إمكانية إعادة التعيين
- **التقدم المرئي**: شريط تقدم لإكمال الأذكار
- **البحث**: بحث نصي في جميع الأذكار
- **المفضلة**: حفظ الأذكار المفضلة
- **النسخ والمشاركة**: لكل ذكر على حدة
- **التنبيهات**: تذكير يومي قابل للتخصيص

#### التنبيهات الذكية
- **أذكار الصباح**: 6:00 ص (قابل للتعديل)
- **أذكار المساء**: 6:00 م (قابل للتعديل)
- **تنبيهات مخصصة**: حسب رغبة المستخدم
- **أنواع التنبيه**: اهتزاز، صوت النظام، صامت

### 2. القرآن الكريم 📖

#### النص والعرض
- **الخط**: عثماني (KFGQPC Uthmanic Script HAFS)
- **التنسيق**: آية آية مع أرقام الآيات
- **التحكم**: تكبير وتصغير الخط
- **الوضع الليلي**: خلفية داكنة مريحة للعين

#### الميزات التفاعلية
- **البحث المتقدم**: 
  - بحث في النص العربي
  - بحث برقم السورة والآية
  - بحث بالكلمات المفتاحية
- **العلامات المرجعية**: حفظ الآيات المفضلة
- **آخر موضع قراءة**: العودة لآخر صفحة مقروءة
- **النسخ والمشاركة**: للآيات المحددة
- **معلومات السور**: 
  - اسم السورة
  - مكية أم مدنية
  - عدد الآيات
  - ترتيب النزول

#### التنظيم والتصفح
- **فهرس السور**: قائمة كاملة بأسماء السور
- **التنقل السريع**: بين السور والصفحات
- **وضع القراءة**: ملء الشاشة بدون تشتيت

### 3. مواقيت الصلاة 🕌

#### طريقة الحساب
- **المرجع الأساسي**: أم القرى (مكة المكرمة)
- **الدقة**: حساب دقيق حسب الموقع الجغرافي
- **التحديث**: يومي تلقائي

#### المواقيت المعروضة
1. **الفجر** 🌅
2. **الشروق** ☀️
3. **الظهر** 🌞
4. **العصر** 🌇
5. **المغرب** 🌆
6. **العشاء** 🌙

#### الميزات التفاعلية
- **العد التنازلي**: للصلاة القادمة
- **التنبيهات**: 
  - عند دخول الوقت
  - قبل الوقت بـ 5، 10، 15 دقيقة (قابل للتخصيص)
- **التعديل اليدوي**: +/- دقائق لكل صلاة
- **التقويم الشهري**: عرض مواقيت الشهر كاملاً

### 4. اتجاه القبلة 🧭

#### التقنية المستخدمة
- **البوصلة الرقمية**: استخدام حساسات الهاتف
- **تحديد الموقع**: GPS للحصول على الإحداثيات الدقيقة
- **الحساب**: خوارزمية دقيقة لحساب الاتجاه

#### الميزات
- **البوصلة المرئية**: عرض اتجاه القبلة بوضوح
- **زاوية الانحراف**: عرض الدرجات من الشمال
- **المسافة**: عرض المسافة إلى مكة المكرمة
- **المعايرة**: إرشادات لمعايرة البوصلة

### 5. أسماء الله الحسنى ✨

#### المحتوى
- **العدد**: 99 اسماً كاملة
- **النص**: عربي فقط بخط جميل
- **التنظيم**: عرض 6 أسماء في كل صفحة

#### الميزات
- **التصفح**: سهولة التنقل بين الأسماء
- **العداد**: تتبع الأسماء المقروءة
- **النسخ والمشاركة**: لكل اسم على حدة
- **البحث**: بحث في الأسماء الحسنى

### 6. المسبحة الإلكترونية 📿

#### الوظائف الأساسية
- **العداد الرقمي**: عد تصاعدي بدون حد أقصى
- **أزرار التحكم**: 
  - زر العد (+1)
  - زر إعادة التعيين (صفر)
  - زر التراجع (-1)
- **الاهتزاز**: عند كل ضغطة (قابل للإلغاء)

#### الميزات المتقدمة
- **الإحصائيات اليومية**: عدد التسبيحات لكل يوم
- **الأهداف**: تحديد هدف يومي للتسبيح
- **التذكير**: تنبيه للتسبيح في أوقات محددة
- **أنواع التسبيح**: 
  - سبحان الله
  - الحمد لله
  - الله أكبر
  - لا إله إلا الله
  - أستغفر الله

### 7. التقويم الهجري 🌙

#### المحتوى
- **التقويم المزدوج**: هجري وميلادي جنباً إلى جنب
- **الأشهر الهجرية**: بالأسماء العربية الصحيحة
- **المناسبات الإسلامية**: 
  - شهر رمضان المبارك
  - عيد الفطر
  - عيد الأضحى
  - يوم عرفة
  - يوم عاشوراء
  - ليلة القدر
  - الإسراء والمعراج
  - مولد النبي ﷺ

#### الميزات التفاعلية
- **التنقل**: بين الأشهر والسنوات
- **التنبيهات**: للمناسبات الإسلامية
- **معلومات إضافية**: عن كل مناسبة
- **التقويم الشخصي**: إضافة مناسبات شخصية

### 8. تقويم الأعمال الصالحة 📅

#### الأعمال المتتبعة
1. **الصلوات الخمس**: تتبع أداء كل صلاة
2. **قراءة القرآن**: عدد الصفحات أو السور
3. **الأذكار اليومية**: إكمال أذكار الصباح والمساء
4. **التسبيح**: عدد التسبيحات اليومية
5. **الصيام**: أيام الصيام (نافلة)
6. **الصدقة**: تسجيل أعمال الخير
7. **الدعاء**: وقت مخصص للدعاء
8. **قيام الليل**: تسجيل أداء قيام الليل

#### نظام التتبع
- **التقييم اليومي**: نجوم أو نسبة مئوية
- **الإحصائيات الأسبوعية**: تقرير أسبوعي
- **الإحصائيات الشهرية**: تقرير شهري
- **الأهداف**: تحديد أهداف شخصية
- **التحفيز**: رسائل تشجيعية وآيات قرآنية

#### الميزات التفاعلية
- **التذكيرات**: تنبيهات للأعمال المجدولة
- **التقارير المرئية**: رسوم بيانية للتقدم
- **المشاركة**: مشاركة الإنجازات (اختيارية)
- **النسخ الاحتياطي**: حفظ البيانات محلياً

---

## 🌐 واجهات برمجة التطبيقات (APIs)

### 1. API مواقيت الصلاة
```
المصدر: Islamic Finder API / Aladhan API
الرابط: https://api.aladhan.com/v1/timings
المعاملات:
- latitude: خط العرض
- longitude: خط الطول  
- method: 4 (أم القرى)
- school: 0 (شافعي)
```

### 2. API اتجاه القبلة
```
المصدر: Islamic Finder API
الرابط: https://api.aladhan.com/v1/qibla
المعاملات:
- latitude: خط العرض
- longitude: خط الطول
```

### 3. API القرآن الكريم
```
المصدر: Quran API / Al-Quran Cloud
الرابط: https://api.alquran.cloud/v1/quran/quran-uthmani
التنسيق: JSON مع النص العثماني
```

### 4. API التقويم الهجري
```
المصدر: Islamic Calendar API
الرابط: https://api.aladhan.com/v1/hijriCalendar
المعاملات:
- year: السنة الهجرية
- month: الشهر الهجري
```

### 5. مصادر البيانات المحلية
- **أسماء الله الحسنى**: ملف JSON محلي
- **الأذكار**: قاعدة بيانات SQLite محلية (حصن المسلم)
- **المناسبات الإسلامية**: ملف JSON محلي

---

## 💾 هيكل قاعدة البيانات

### جداول قاعدة البيانات المحلية

#### 1. جدول الأذكار (azkar)
```sql
CREATE TABLE azkar (
    id INTEGER PRIMARY KEY,
    category_id INTEGER,
    arabic_text TEXT NOT NULL,
    repeat_count INTEGER DEFAULT 1,
    source TEXT,
    benefits TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. جدول تصنيفات الأذكار (azkar_categories)
```sql
CREATE TABLE azkar_categories (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT,
    order_index INTEGER,
    description TEXT
);
```

#### 3. جدول تقدم المستخدم (user_progress)
```sql
CREATE TABLE user_progress (
    id INTEGER PRIMARY KEY,
    azkar_id INTEGER,
    completed_count INTEGER DEFAULT 0,
    last_completed DATE,
    total_completed INTEGER DEFAULT 0,
    FOREIGN KEY (azkar_id) REFERENCES azkar(id)
);
```

#### 4. جدول العلامات المرجعية (bookmarks)
```sql
CREATE TABLE bookmarks (
    id INTEGER PRIMARY KEY,
    type TEXT NOT NULL, -- 'quran', 'azkar', 'names'
    reference_id INTEGER,
    surah_number INTEGER,
    ayah_number INTEGER,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. جدول الأعمال الصالحة (good_deeds)
```sql
CREATE TABLE good_deeds (
    id INTEGER PRIMARY KEY,
    deed_type TEXT NOT NULL,
    date DATE NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    count INTEGER DEFAULT 0,
    notes TEXT
);
```

#### 6. جدول الإعدادات (settings)
```sql
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔔 نظام الإشعارات والتنبيهات

### أنواع الإشعارات

#### 1. إشعارات مواقيت الصلاة
- **التوقيت**: عند دخول كل وقت صلاة
- **التنبيه المسبق**: 5، 10، 15 دقيقة قبل الوقت
- **النوع**: اهتزاز + صوت النظام
- **النص**: "حان وقت صلاة [اسم الصلاة]"

#### 2. إشعارات الأذكار
- **أذكار الصباح**: 6:00 ص (قابل للتعديل)
- **أذكار المساء**: 6:00 م (قابل للتعديل)
- **تذكير مخصص**: حسب اختيار المستخدم
- **النص**: "وقت أذكار [الصباح/المساء]"

#### 3. إشعارات الأعمال الصالحة
- **التذكير اليومي**: لإكمال الأعمال المجدولة
- **التحفيز**: رسائل تشجيعية
- **الإنجازات**: عند تحقيق الأهداف

### إعدادات التحكم
- **تفعيل/إلغاء**: لكل نوع إشعار
- **أوقات الصمت**: فترات عدم الإزعاج
- **نوع التنبيه**: اهتزاز، صوت، صامت
- **تكرار التنبيه**: مرة واحدة أو متكرر

---

## 🎯 تجربة المستخدم (UX)

### الشاشة الرئيسية
- **التخطيط**: شبكة 2×3 للميزات الأساسية
- **الوقت الحالي**: عرض الوقت والتاريخ الهجري/الميلادي
- **الصلاة القادمة**: عداد تنازلي مع اسم الصلاة
- **الوصول السريع**: للأذكار والقرآن

### التنقل
- **الشريط السفلي**: 5 تبويبات رئيسية
  1. الرئيسية 🏠
  2. الأذكار 📿
  3. القرآن 📖
  4. المواقيت 🕌
  5. المزيد ⚙️
- **القوائم الجانبية**: للوصول السريع للأقسام
- **أزرار العودة**: في كل شاشة فرعية

### إمكانية الوصول
- **أحجام الخط**: 4 مستويات (صغير إلى كبير جداً)
- **التباين العالي**: للمستخدمين ضعاف البصر
- **الاهتزاز**: تأكيد اللمس للمكفوفين
- **الأصوات**: أصوات النظام للتنقل

---

## 🔒 الأمان والخصوصية

### حماية البيانات
- **التخزين المحلي**: جميع البيانات الشخصية محلية
- **عدم جمع البيانات**: لا يتم إرسال بيانات شخصية للخوادم
- **الموقع**: يُستخدم فقط لحساب المواقيت والقبلة
- **الأذونات المطلوبة**:
  - الموقع (للمواقيت والقبلة)
  - التخزين (لحفظ البيانات)
  - الاهتزاز (للتنبيهات)
  - الإشعارات (للتذكير)

### الشفافية
- **سياسة الخصوصية**: واضحة ومفصلة
- **مصادر البيانات**: ذكر جميع المصادر المستخدمة
- **الأذونات**: شرح سبب كل إذن مطلوب

---

## 📊 متطلبات الأداء

### السرعة والاستجابة
- **وقت التشغيل**: أقل من 3 ثوانٍ
- **التنقل**: استجابة فورية (أقل من 100ms)
- **البحث**: نتائج في أقل من ثانية واحدة
- **تحديث المواقيت**: أقل من 5 ثوانٍ

### استهلاك الموارد
- **الذاكرة**: أقل من 100 MB
- **التخزين**: أقل من 50 MB
- **البطارية**: استهلاك منخفض
- **البيانات**: الحد الأدنى للاستخدام

### التوافق
- **الأجهزة**: دعم الأجهزة منخفضة المواصفات
- **الشاشات**: تجاوب مع جميع أحجام الشاشات
- **الاتجاهات**: دعم الوضع العمودي والأفقي

---

## 🚀 خطة التطوير والإطلاق

### المرحلة الأولى (MVP) - 8 أسابيع
1. **الأسبوع 1-2**: إعداد المشروع والتصميم الأساسي
2. **الأسبوع 3-4**: تطوير الأذكار ومواقيت الصلاة
3. **الأسبوع 5-6**: تطوير القرآن الكريم واتجاه القبلة
4. **الأسبوع 7-8**: الاختبار والتحسينات

### المرحلة الثانية - 4 أسابيع
1. **الأسبوع 9-10**: أسماء الله الحسنى والمسبحة
2. **الأسبوع 11-12**: التقويم الهجري وتقويم الأعمال الصالحة

### المرحلة الثالثة - 2 أسابيع
1. **الأسبوع 13-14**: التحسينات النهائية والاختبار الشامل

### الاختبار والجودة
- **اختبار الوحدة**: لكل ميزة على حدة
- **اختبار التكامل**: للتأكد من عمل الميزات معاً
- **اختبار المستخدم**: مع مجموعة من المستخدمين المستهدفين
- **اختبار الأداء**: على أجهزة مختلفة

---

## 💰 التكلفة المقدرة

### تكاليف التطوير
- **المطور الرئيسي**: 12 أسبوع × $800 = $9,600
- **مصمم UI/UX**: 4 أسابيع × $600 = $2,400
- **مطور مساعد**: 8 أسابيع × $500 = $4,000
- **اختبار الجودة**: 2 أسبوع × $400 = $800

### تكاليف إضافية
- **رخص الخطوط**: $200
- **حساب المطور Google Play**: $25
- **أدوات التطوير**: $300
- **الاستضافة والـ APIs**: $100/شهر

### إجمالي التكلفة المقدرة
**التطوير**: $17,125
**التشغيل السنوي**: $1,200

---

## 📈 خطة التسويق والنشر

### استراتيجية الإطلاق
1. **الإطلاق التجريبي**: مع مجموعة محدودة من المستخدمين
2. **جمع التغذية الراجعة**: وتطبيق التحسينات
3. **الإطلاق العام**: على متجر Google Play
4. **التسويق الرقمي**: عبر وسائل التواصل الاجتماعي

### قنوات التسويق
- **المساجد المحلية**: توزيع معلومات عن التطبيق
- **وسائل التواصل الاجتماعي**: فيسبوك، تويتر، إنستغرام
- **المؤثرين الدينيين**: مراجعات وتوصيات
- **المواقع الإسلامية**: إعلانات مدفوعة

### مؤشرات النجاح
- **التحميلات**: 10,000 تحميل في الشهر الأول
- **التقييم**: 4.5+ نجوم على متجر التطبيقات
- **الاستخدام النشط**: 70% من المستخدمين يستخدمون التطبيق يومياً
- **الاحتفاظ**: 60% من المستخدمين يستمرون لأكثر من شهر

---

## 🔄 خطة التحديث والصيانة

### التحديثات الدورية
- **تحديثات الأمان**: شهرياً
- **إصلاح الأخطاء**: حسب الحاجة
- **ميزات جديدة**: كل 3 أشهر
- **تحديث البيانات**: سنوياً (مواقيت، تقويم)

### الدعم الفني
- **البريد الإلكتروني**: للاستفسارات والمشاكل
- **الأسئلة الشائعة**: داخل التطبيق
- **التحديثات التلقائية**: للبيانات المهمة
- **النسخ الاحتياطي**: للبيانات الشخصية

### التطوير المستقبلي
- **ميزات إضافية**: حسب طلب المستخدمين
- **دعم لغات أخرى**: للواجهة (إنجليزية، فرنسية)
- **نسخة iOS**: بعد نجاح النسخة الأولى
- **ميزات متقدمة**: ذكاء اصطناعي للتوصيات

---

## 📋 الخلاصة والتوصيات

### نقاط القوة المتوقعة
1. **الشمولية**: تطبيق واحد لجميع الاحتياجات الإسلامية اليومية
2. **البساطة**: واجهة سهلة الاستخدام لجميع الأعمار
3. **العمل بدون إنترنت**: معظم الميزات تعمل محلياً
4. **الدقة**: مصادر موثوقة ومعتمدة
5. **التخصيص**: إعدادات قابلة للتخصيص حسب الحاجة

### التحديات المحتملة
1. **حجم التطبيق**: قد يكون كبيراً بسبب النصوص الكثيرة
2. **دقة الموقع**: قد تتأثر في المناطق النائية
3. **استهلاك البطارية**: بسبب الإشعارات المتكررة
4. **التحديثات**: الحاجة لتحديث البيانات دورياً

### التوصيات للنجاح
1. **اختبار مكثف**: على أجهزة وبيئات مختلفة
2. **تغذية راجعة مستمرة**: من المستخدمين
3. **تحسين مستمر**: للأداء وتجربة المستخدم
4. **شراكات استراتيجية**: مع المؤسسات الإسلامية
5. **تسويق مدروس**: يركز على القيمة الدينية والعملية

---

**تم إعداد هذه الوثيقة بعناية لتكون دليلاً شاملاً لتطوير تطبيق "أذكار المسلم". نسأل الله التوفيق والسداد في هذا العمل المبارك.**

---

*آخر تحديث: أغسطس 2025*
*إعداد: فريق التطوير التقني*
